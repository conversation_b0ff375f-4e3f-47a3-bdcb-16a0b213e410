# GitHub Copilot Context Instructions

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Context

This is a Python-based MCP (Model Context Protocol) client implementation that automates browser interactions for bill information retrieval. The project uses Playwright for browser automation and implements custom MCP handlers for specific webpage interactions.

## Key Components

- MCPClient: Main class that handles browser automation and data extraction
- Playwright: Used for browser control and page interactions
- Error handling: Implements robust error handling for network and page load issues

## Code Style Preferences

- Use Python type hints where applicable
- Implement proper error handling with try-except blocks
- Add detailed docstrings for functions and classes
- Follow PEP 8 style guidelines

## Common Tasks

- Browser automation using Playwright
- DOM element selection and interaction
- Data extraction from web pages
- Error handling and logging
