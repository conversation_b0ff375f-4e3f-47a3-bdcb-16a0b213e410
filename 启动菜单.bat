@echo off
title Bill-Helper 项目启动菜单
color 0A
chcp 65001 >nul

:menu
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Bill-Helper 项目启动菜单                    ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  1. 账单助手系统 📋                                           ║
echo ║     - Web界面的账单到期时间提取工具                           ║
echo ║     - 访问地址：http://localhost:5001                        ║
echo ║                                                              ║
echo ║  2. 聚合二维码流水匹配器 🔍                                    ║
echo ║     - 智能匹配聚合二维码交易与银行流水                        ║
echo ║     - 需要：TZ.xlsx + LS.xlsx                                ║
echo ║                                                              ║
echo ║  3. 多文件内容填充工具 📊                                      ║
echo ║     - 批量处理Excel文件数据填充                               ║
echo ║     - 目标文件：TTXW.xlsx                                    ║
echo ║                                                              ║
echo ║  4. 查看使用指南 📖                                           ║
echo ║                                                              ║
echo ║  5. 退出 ❌                                                   ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
set /p choice="请选择要启动的功能 (1-5): "

if "%choice%"=="1" goto bill_helper
if "%choice%"=="2" goto qr_matcher
if "%choice%"=="3" goto file_filler
if "%choice%"=="4" goto show_guide
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入...
timeout /t 2 >nul
goto menu

:bill_helper
cls
echo.
echo ═══════════════════════════════════════
echo           启动账单助手系统
echo ═══════════════════════════════════════
echo.
echo 正在启动账单助手系统...
echo 启动后请在浏览器中访问: http://localhost:5001
echo.
call "启动账单助手.bat"
goto menu

:qr_matcher
cls
echo.
echo ═══════════════════════════════════════
echo        启动聚合二维码流水匹配器
echo ═══════════════════════════════════════
echo.
call "启动流水匹配器.bat"
goto menu

:file_filler
cls
echo.
echo ═══════════════════════════════════════
echo        启动多文件内容填充工具
echo ═══════════════════════════════════════
echo.
call "启动填充工具.bat"
goto menu

:show_guide
cls
echo.
echo ═══════════════════════════════════════
echo            查看使用指南
echo ═══════════════════════════════════════
echo.
if exist "使用指南.md" (
    echo 正在打开使用指南...
    start "" "使用指南.md"
    echo 使用指南已在默认编辑器中打开
) else (
    echo 错误：找不到使用指南.md文件
)
echo.
pause
goto menu

:exit
cls
echo.
echo ═══════════════════════════════════════
echo              感谢使用！
echo ═══════════════════════════════════════
echo.
echo 👋 再见！
timeout /t 2 >nul
exit 