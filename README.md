# Bill-Helper 项目

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3.3-green.svg)](https://flask.palletsprojects.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📋 项目概述

Bill-Helper 是一个综合性的财务数据处理和自动化工具集，专为提高财务数据处理、流水匹配和文件管理效率而设计。该项目整合了Web应用、智能匹配算法和批量数据处理功能，为用户提供完整的数据处理解决方案。

### 🎯 核心功能模块

| 模块 | 功能描述 | 技术栈 | 界面类型 |
|------|----------|--------|----------|
| **账单助手系统** | 自动化提取订单账单到期时间 | Flask + Playwright | Web界面 |
| **聚合二维码流水匹配器** | 智能匹配聚合二维码交易与银行流水 | Pandas + 智能算法 | 命令行 |
| **多文件内容填充工具** | 批量处理Excel文件数据填充 | Openpyxl + 数据处理 | 交互式菜单 |

### 💡 项目价值

**解决的痛点：**
- ❌ 手工查询订单账单到期时间，效率低下
- ❌ 聚合二维码交易与银行流水匹配复杂，容易出错
- ❌ 多个Excel文件数据整合耗时费力，重复劳动

**提供的价值：**
- ✅ 自动化处理，显著提高工作效率
- ✅ 智能匹配算法，确保数据准确性
- ✅ 批量处理能力，减少重复操作
- ✅ 用户友好界面，降低使用门槛

### 👥 目标用户

- 💼 **财务人员** - 账单处理、流水匹配
- 🏢 **运营人员** - 订单管理、数据整理
- 📊 **数据分析师** - 数据处理、报表生成
- 👨‍💼 **管理层人员** - 业务监控、决策支持

## 🚀 功能模块详解

### 📋 账单助手系统

> **目标：** 自动化提取订单账单到期时间，告别手工查询

#### ✨ 核心功能
- 🔄 **智能文件处理** - 支持 .xls/.xlsx 格式，自动验证文件结构
- 🔐 **自动化登录** - 智能登录运营管理平台（支持新旧系统）
- 📅 **账单数据提取** - 自动提取每期账单到期日期
- 📊 **数据格式化** - 生成标准化Excel报表
- ⚡ **异步任务处理** - 实时进度显示，不阻塞界面

#### 🛠️ 技术架构
```
账单助手系统架构
├── Flask Web服务 (app.py)
├── 认证模块 (modules/auth/)
│   └── 智能登录检测
├── 爬虫模块 (modules/crawler/)
│   └── Playwright浏览器自动化
├── 数据处理模块 (modules/processor/)
│   └── Excel数据处理
└── 导出模块 (modules/exporter/)
    └── 结果文件生成
```

#### 📝 使用流程
```bash
# 1. 启动服务
cd bill-helper && python app.py

# 2. 访问界面
# 浏览器打开: http://localhost:5001

# 3. 操作步骤
# ① 上传包含"订单ID"列的Excel文件
# ② 输入系统用户名和密码
# ③ 选择系统类型（新系统/旧系统）
# ④ 点击"开始处理"
# ⑤ 查看实时进度并下载结果
```

---

### 🔍 聚合二维码流水匹配器

> **目标：** 智能匹配聚合二维码交易与银行流水，提高匹配准确率

#### ✨ 核心功能
- 🧠 **智能匹配算法** - 基于时间、金额、客户等多维度匹配
- 🎯 **四层优先级策略** - 确保最优匹配结果
- ⚖️ **冲突自动解决** - 保证匹配结果唯一性
- 📈 **详细分析报告** - 生成匹配质量统计

#### 🔬 匹配策略详解
| 优先级 | 权重 | 说明 |
|--------|------|------|
| **时间优先级** | 40% | 时间差越小越优先，24小时内线性衰减 |
| **金额优先级** | 25% | 大金额优先（≥1万:1.0, ≥5千:0.9, ≥1千:0.8） |
| **客户唯一性** | 20% | 单一客户单日同金额交易优先 |
| **订单完整性** | 15% | 有备注信息的订单优先 |

#### 📁 输出文件说明
```
匹配结果文件
├── TZ_完整匹配结果.xlsx          # 原始数据+匹配结果
├── 聚合二维码匹配结果.xlsx        # 仅聚合二维码交易结果
├── 综合匹配报告.xlsx             # 详细分析报告
│   ├── 匹配统计
│   ├── 已匹配记录
│   └── 未匹配记录
└── 聚合二维码交易记录_提取.xlsx   # 原始提取数据
```

#### 📝 使用流程
```bash
# 1. 准备文件
# 确保当前目录有：TZ.xlsx（交易数据）+ LS.xlsx（银行流水）

# 2. 运行程序
python qr_flow_matcher.py

# 3. 操作步骤
# ① 查看数据概览和统计信息
# ② 确认执行匹配（输入 y/yes）
# ③ 查看匹配结果和质量分布
# ④ 检查生成的结果文件
```

---

### 📊 多文件内容填充工具

> **目标：** 批量处理Excel文件，自动化数据整合

#### ✨ 核心功能
- 📁 **批量文件处理** - 同时处理多个Excel文件
- 🔄 **智能数据映射** - 自动处理10个还款期数据
- 📋 **双表同步填充** - 同时更新"订单管理"和"资金流水账"
- 📅 **自动数据排序** - 按日期智能排序

#### 🔧 数据处理逻辑
```
数据处理流程
├── 基础字段映射 (A-M列)
│   ├── 订单编号、日期、客户信息
│   └── 产品类型、台数、期数等
├── 还款期数据处理 (N-W列)
│   ├── 自动处理最多10期数据
│   └── 智能补齐缺失期数
├── 自动生成记录
│   ├── 放款记录
│   └── 供应商利润记录
└── 格式转换
    └── 日期格式自动识别转换
```

#### 🎛️ 交互菜单
```
多文件填充工具菜单
┌─────────────────────────────┐
│ 1. 选择源文件               │
│ 2. 预览数据                 │
│ 3. 执行填充                 │
│ 4. 查看已选择文件           │
│ 5. 设置目标文件             │
│ 6. 退出                     │
└─────────────────────────────┘
```

#### 📝 使用流程
```bash
# 1. 准备文件
# 确保有目标文件：TTXW.xlsx

# 2. 运行程序
python multi_file_filler_correct.py

# 3. 操作步骤
# ① 选择源文件（支持多选或全选）
# ② 预览数据格式（可选）
# ③ 执行填充操作
# ④ 确认结果
```

## 🏗️ 系统架构

### 📐 整体架构设计

```mermaid
graph TB
    subgraph "Bill-Helper 项目架构"
        subgraph "账单助手系统 (Web应用)"
            A1[Flask Web服务]
            A2[Playwright爬虫引擎]
            A3[异步任务处理]
            A4[数据处理模块]
        end

        subgraph "聚合二维码流水匹配器 (命令行应用)"
            B1[数据提取器]
            B2[智能匹配算法]
            B3[结果合并器]
            B4[报告生成器]
        end

        subgraph "多文件内容填充工具 (交互式应用)"
            C1[文件扫描器]
            C2[数据处理器]
            C3[Excel写入器]
            C4[菜单控制器]
        end

        subgraph "共享组件"
            D1[Excel处理库]
            D2[数据验证器]
            D3[日志系统]
            D4[配置管理]
        end
    end

    A1 --> A2
    A2 --> A3
    A3 --> A4
    B1 --> B2
    B2 --> B3
    B3 --> B4
    C1 --> C2
    C2 --> C3
    C3 --> C4

    A4 -.-> D1
    B4 -.-> D1
    C3 -.-> D1
    A1 -.-> D4
    B1 -.-> D4
    C1 -.-> D4
```

### 📁 项目文件结构

```
Bill-Helper/                           # 项目根目录
├── 📁 bill-helper/                    # 账单助手系统
│   ├── 🐍 app.py                     # Flask Web应用主入口
│   ├── ⚙️ config.py                  # 系统配置文件
│   ├── 📁 modules/                   # 功能模块目录
│   │   ├── 📁 auth/                  # 认证模块
│   │   │   └── 🐍 login.py           # 智能登录处理
│   │   ├── 📁 crawler/               # 爬虫模块
│   │   │   └── 🐍 bill_crawler.py    # 账单数据抓取
│   │   ├── 📁 processor/             # 数据处理模块
│   │   │   └── 🐍 data_processor.py  # 数据处理逻辑
│   │   └── 📁 exporter/              # 导出模块
│   │       └── 🐍 excel_exporter.py  # Excel导出功能
│   ├── 📁 templates/                 # HTML模板文件
│   │   └── 🌐 index.html             # Web界面模板
│   ├── 📁 static/                    # 静态资源文件
│   ├── 📁 uploads/                   # 文件上传目录
│   ├── 📁 processed/                 # 处理结果目录
│   └── 📄 requirements.txt           # Python依赖包
├── 🐍 qr_flow_matcher.py             # 流水匹配器主程序
├── 🐍 enhanced_qr_matcher.py         # 增强匹配算法
├── 🐍 qr_data_merger.py              # 数据合并处理
├── 🐍 qr_transaction_extractor.py    # 交易数据提取
├── 🐍 multi_file_filler_correct.py   # 多文件填充工具
├── 📁 ExcelDataMatcher/              # 数据匹配相关文档
├── 📄 使用指南.md                    # 详细使用指南
├── 📄 产品需求文档.md                # 产品需求说明
├── 🚀 启动菜单.bat                   # 主启动菜单
├── 🚀 启动账单助手.bat               # 账单助手启动器
├── 🚀 启动流水匹配器.bat             # 流水匹配器启动器
├── 🚀 启动填充工具.bat               # 填充工具启动器
├── 📄 requirements.txt               # 项目依赖包
└── 📖 README.md                      # 项目文档
```

### 🔧 技术栈详解

#### 后端技术
| 技术 | 版本 | 用途 |
|------|------|------|
| **Python** | 3.8+ | 主要开发语言 |
| **Flask** | 2.3.3 | Web框架 |
| **Playwright** | 1.40.0 | 浏览器自动化 |
| **Pandas** | 2.0.3 | 数据处理 |
| **Openpyxl** | 3.1.2 | Excel文件操作 |
| **Xlrd** | 2.0.1 | Excel文件读取 |

#### 数据处理
- **文件格式支持：** Excel (.xlsx, .xls)
- **数据编码：** UTF-8
- **日期处理：** 多格式自动识别
- **内存优化：** 分批处理大文件

## ⚡ 快速开始

### 📋 环境要求

| 要求 | 最低版本 | 推荐版本 | 说明 |
|------|----------|----------|------|
| **Python** | 3.8 | 3.9+ | 主要运行环境 |
| **操作系统** | - | Windows 10+ | 支持 Windows/macOS/Linux |
| **浏览器** | - | Chrome/Edge | 账单助手需要 |
| **内存** | 4GB | 8GB+ | 处理大文件时需要 |
| **磁盘空间** | 1GB | 2GB+ | 包含依赖和数据文件 |

### 🛠️ 安装步骤

#### 方式一：标准安装（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd Bill-Helper

# 2. 创建虚拟环境
python -m venv venv

# 3. 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt

# 5. 安装浏览器（账单助手需要）
python -m playwright install chromium
```

#### 方式二：快速安装

```bash
# 一键安装脚本（Windows）
# 创建 install.bat 文件并运行
@echo off
python -m venv venv
call venv\Scripts\activate
pip install -r requirements.txt
python -m playwright install chromium
echo 安装完成！
pause
```

### 🚀 启动方式

#### 🎯 方式一：启动菜单（推荐新手）

```bash
# Windows 用户
双击 "启动菜单.bat" 文件

# 或命令行运行
启动菜单.bat
```

**启动菜单功能：**
- 📋 启动账单助手系统
- 🔍 启动聚合二维码流水匹配器
- 📊 启动多文件内容填充工具
- 📖 查看使用指南
- ❌ 退出程序

#### ⚡ 方式二：直接启动（推荐熟练用户）

```bash
# 账单助手系统（Web界面）
python bill-helper/app.py
# 访问: http://localhost:5001

# 聚合二维码流水匹配器（命令行）
python qr_flow_matcher.py

# 多文件内容填充工具（交互式）
python multi_file_filler_correct.py
```

#### 🔧 方式三：独立启动器

```bash
# 使用专用启动器
启动账单助手.bat      # 仅启动账单助手
启动流水匹配器.bat    # 仅启动流水匹配器
启动填充工具.bat      # 仅启动填充工具
```

## 📖 使用指南

### 📋 账单助手系统使用指南

#### 🎯 适用场景
- 需要批量查询订单账单到期时间
- 从运营管理平台提取账单数据
- 生成包含账单信息的Excel报表

#### 📝 详细步骤

**第一步：准备Excel文件**
```
要求：Excel文件必须包含"订单ID"列
示例格式：
┌─────────────────────────┬──────────┬──────────┐
│ 订单ID                  │ 客户名称 │ 产品信息 │
├─────────────────────────┼──────────┼──────────┤
│ OI1805817558897655808   │ 张三     │ 产品A    │
│ OI1805817558897655809   │ 李四     │ 产品B    │
└─────────────────────────┴──────────┴──────────┘
```

**第二步：启动系统**
```bash
# 方法1：使用启动菜单
启动菜单.bat → 选择 "1. 账单助手系统"

# 方法2：直接启动
cd bill-helper && python app.py
```

**第三步：Web界面操作**
1. 🌐 **访问界面** - 浏览器打开 `http://localhost:5001`
2. 📁 **上传文件** - 点击"选择文件"，选择Excel文件
3. 🔐 **配置登录** - 输入系统用户名和密码
4. ⚙️ **选择系统** - 选择"新系统"或"旧系统"
5. ▶️ **开始处理** - 点击"开始处理"按钮
6. 📊 **查看进度** - 实时查看处理进度
7. ⬇️ **下载结果** - 处理完成后下载结果文件

**输出文件格式：**
```
处理后的订单数据_时间戳.xlsx
┌─────────────────────────┬──────────┬─────────────┬──────────┬──────────┐
│ 订单ID                  │ 客户名称 │ 账单到期日期│ 第一期   │ 第二期   │
├─────────────────────────┼──────────┼─────────────┼──────────┼──────────┤
│ OI1805817558897655808   │ 张三     │ 2025-06-01  │2025-06-01│2025-07-01│
└─────────────────────────┴──────────┴─────────────┴──────────┴──────────┘
```

---

### 🔍 聚合二维码流水匹配器使用指南

#### 🎯 适用场景
- 聚合二维码交易与银行流水对账
- 自动化交易匹配，提高财务处理效率
- 生成详细的匹配分析报告

#### 📝 详细步骤

**第一步：准备数据文件**
```
必需文件：
├── TZ.xlsx  # 交易数据文件
│   ├── 必须包含"交易类型"列
│   ├── 必须有"聚合二维码"类型记录
│   └── 包含客户姓名、交易金额、日期时间等
└── LS.xlsx  # 银行流水文件
    ├── 必须包含"交易时间"列
    ├── 必须包含"交易金额"列
    └── 必须包含"交易参考号"列
```

**第二步：运行匹配器**
```bash
# 确保文件在当前目录
ls TZ.xlsx LS.xlsx

# 启动匹配器
python qr_flow_matcher.py
```

**第三步：交互式操作**
1. 📊 **查看数据概览** - 系统显示数据统计信息
2. ✅ **确认执行** - 输入 `y` 或 `yes` 确认开始匹配
3. ⏳ **等待处理** - 系统执行智能匹配算法
4. 📈 **查看结果** - 显示匹配成功率和质量分布
5. 📁 **检查文件** - 查看生成的结果文件

**输出文件说明：**
- `TZ_完整匹配结果.xlsx` - 完整数据+匹配结果
- `聚合二维码匹配结果.xlsx` - 仅聚合二维码结果
- `综合匹配报告.xlsx` - 详细分析报告
- `聚合二维码交易记录_提取.xlsx` - 原始提取数据

---

### 📊 多文件内容填充工具使用指南

#### 🎯 适用场景
- 批量处理多个Excel文件数据整合
- 自动填充订单管理和资金流水账表
- 标准化数据格式和结构

#### 📝 详细步骤

**第一步：准备文件**
```
文件要求：
├── TTXW.xlsx  # 目标文件（必须存在）
│   ├── "订单管理" 工作表
│   └── "资金流水账" 工作表
└── 源文件/     # 源Excel文件（多个）
    ├── 包含订单数据
    ├── 包含还款期信息（最多10期）
    └── 标准格式的Excel文件
```

**第二步：启动工具**
```bash
# 确保目标文件存在
ls TTXW.xlsx

# 启动填充工具
python multi_file_filler_correct.py
```

**第三步：交互式操作**

```
多文件填充工具菜单
┌─────────────────────────────┐
│ 1. 选择源文件               │  ← 选择要处理的Excel文件
│ 2. 预览数据                 │  ← 预览处理后的数据格式
│ 3. 执行填充                 │  ← 执行数据填充操作
│ 4. 查看已选择文件           │  ← 显示当前选中的文件
│ 5. 设置目标文件             │  ← 更改目标文件名
│ 6. 退出                     │  ← 退出程序
└─────────────────────────────┘
```

**操作流程：**
1. 📁 **选择源文件** - 输入文件序号（如：1,3,5）或 `all` 选择全部
2. 👀 **预览数据**（可选）- 查看数据格式是否正确
3. ▶️ **执行填充** - 确认操作（会清空目标文件现有数据）
4. ✅ **完成处理** - 等待处理完成

**数据处理逻辑：**
- 🔄 自动处理10个还款期数据
- 📅 按日期排序数据
- 📋 同时填充两个工作表
- 💰 生成放款记录和供应商利润记录

⚠️ **注意事项：**
- 操作前建议备份目标文件
- 程序会清空目标文件第5行及以后的所有数据
- 确保源文件格式符合预期

## 📋 文件格式要求

### 📋 账单助手系统文件格式

#### 📥 输入文件要求
```
Excel文件格式要求：
✅ 文件格式：.xls 或 .xlsx
✅ 必须包含：订单ID 列（列名可以是"订单ID"、"订单编号"等）
✅ 文件大小：≤ 16MB
✅ 编码格式：UTF-8

示例格式：
┌─────────────────────────┬──────────┬──────────┬─────────┐
│ 订单ID                  │ 客户名称 │ 产品信息 │ 其他信息│
├─────────────────────────┼──────────┼──────────┼─────────┤
│ OI1805817558897655808   │ 张三     │ 产品A    │ ...     │
│ OI1805817558897655809   │ 李四     │ 产品B    │ ...     │
└─────────────────────────┴──────────┴──────────┴─────────┘
```

#### 📤 输出文件格式
```
处理后的订单数据_时间戳.xlsx
┌─────────────────────────┬──────────┬─────────────┬──────────┬──────────┬─────┐
│ 订单ID                  │ 客户名称 │ 账单到期日期│ 第一期   │ 第二期   │ ... │
├─────────────────────────┼──────────┼─────────────┼──────────┼──────────┼─────┤
│ OI1805817558897655808   │ 张三     │ 2025-06-01  │2025-06-01│2025-07-01│ ... │
│ OI1805817558897655809   │ 李四     │ 2025-06-15  │2025-06-15│2025-07-15│ ... │
└─────────────────────────┴──────────┴─────────────┴──────────┴──────────┴─────┘
```

---

### 🔍 聚合二维码流水匹配器文件格式

#### 📥 TZ.xlsx（交易数据文件）要求
```
必需列：
✅ 交易类型      # 必须包含"聚合二维码"类型记录
✅ 客户姓名      # 用于匹配客户
✅ 交易金额      # 用于金额匹配
✅ 日期          # 交易日期
✅ 时间          # 交易时间

可选列：
📝 备注          # 提高匹配优先级
📝 订单编号      # 增强匹配准确性
📝 其他业务字段  # 保留原有数据结构
```

#### 📥 LS.xlsx（银行流水文件）要求
```
必需列：
✅ 交易时间      # 银行流水时间
✅ 交易金额      # 流水金额
✅ 交易参考号    # 银行流水唯一标识

可选列：
📝 账户信息      # 银行账户相关信息
📝 交易描述      # 流水描述信息
📝 其他银行字段  # 保留银行流水完整信息
```

#### 📤 输出文件说明
```
生成文件列表：
├── TZ_完整匹配结果.xlsx          # 原始TZ数据 + 匹配的银行流水信息
├── 聚合二维码匹配结果.xlsx        # 仅包含聚合二维码交易的匹配结果
├── 综合匹配报告.xlsx             # 详细分析报告
│   ├── 匹配统计 Sheet            # 匹配成功率、质量分布等统计
│   ├── 已匹配记录 Sheet          # 成功匹配的记录详情
│   └── 未匹配记录 Sheet          # 未能匹配的记录及原因
└── 聚合二维码交易记录_提取.xlsx   # 从TZ.xlsx提取的原始聚合二维码数据
```

---

### 📊 多文件内容填充工具文件格式

#### 📥 源文件要求
```
Excel文件格式：
✅ 文件格式：.xlsx（推荐）或 .xls
✅ 数据结构：标准订单数据格式
✅ 必需字段：
   - 日期相关字段
   - 订单编号
   - 客户信息
   - 还款期数据（最多支持10期）

字段映射（A-M列）：
A: 日期          B: 订单编号      C: 客户姓名      D: 手机号
E: 产品类型      F: 台数          G: 期数          H: 每期还款金
I: 合同金额      J: 店铺归属      K: 客服人员      L: 业务归属
M: 备注

还款期字段（N-W列）：
N-S: 第1-6期还款日期
T-W: 第7-10期还款日期（如适用）
```

#### 📥 目标文件要求
```
TTXW.xlsx 文件结构：
✅ 必须包含工作表：
   - "订单管理" Sheet
   - "资金流水账" Sheet
✅ 文件权限：可读写
✅ 格式要求：Excel 2007+ (.xlsx)

⚠️ 重要提醒：
- 程序会清空目标文件第5行及以后的所有数据
- 建议在操作前备份目标文件
- 确保文件未被其他程序占用
```

## ⚡ 性能与安全

### 📊 性能指标

| 功能模块 | 处理能力 | 平均耗时 | 内存占用 |
|----------|----------|----------|----------|
| **账单助手** | 1000+ 订单 | 5秒/订单 | 200-500MB |
| **流水匹配器** | 10,000+ 交易记录 | 2-5分钟 | 100-300MB |
| **填充工具** | 多个大型Excel文件 | 1-3分钟 | 50-200MB |

### 🔧 性能优化策略

#### 内存管理
- ✅ **分批处理** - 大文件分块读取，避免内存溢出
- ✅ **数据流式处理** - 边读边处理，减少内存占用
- ✅ **垃圾回收优化** - 及时释放不需要的对象

#### 处理效率
- ✅ **异步任务处理** - Web界面不阻塞，后台处理
- ✅ **智能缓存** - 重复数据缓存，减少重复计算
- ✅ **并发处理** - 多线程处理提高效率

#### 网络优化
- ✅ **连接池管理** - 复用浏览器连接
- ✅ **超时控制** - 避免长时间等待
- ✅ **重试机制** - 网络异常自动重试

### 🔒 安全保障

#### 数据安全
```
安全措施清单：
🔐 登录信息安全处理      # 密码不明文存储
🔐 文件类型验证          # 仅允许Excel格式
🔐 文件大小限制          # 防止大文件攻击
🔐 随机文件名            # 防止文件覆盖
🔐 敏感信息脱敏          # 日志中隐藏敏感数据
🔐 本地数据处理          # 数据不上传外部服务器
```

#### 访问控制
- ✅ **本地服务** - Web服务仅监听本地端口
- ✅ **会话管理** - 安全的会话密钥管理
- ✅ **文件权限** - 严格的文件访问权限控制

#### 错误处理
- ✅ **异常捕获** - 完善的异常处理机制
- ✅ **错误日志** - 详细的错误记录和追踪
- ✅ **优雅降级** - 部分功能失败不影响整体运行

## ❓ 常见问题与解决方案

### 🔧 环境配置问题

#### Q1: 提示"模块未找到"或"No module named"
```bash
# 解决方案：
# 1. 确认虚拟环境已激活
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate     # Windows

# 2. 重新安装依赖
pip install -r requirements.txt

# 3. 检查Python版本
python --version  # 确保 >= 3.8

# 4. 如果仍有问题，尝试升级pip
python -m pip install --upgrade pip
```

#### Q2: 账单助手无法访问网页
```bash
# 问题现象：浏览器无法打开 http://localhost:5001

# 解决方案：
# 1. 检查端口占用
netstat -ano | findstr :5001  # Windows
lsof -i :5001                 # macOS/Linux

# 2. 更换端口（修改 app.py）
app.run(host='0.0.0.0', port=5002, debug=True)

# 3. 检查防火墙设置
# 确保防火墙允许Python访问网络

# 4. 尝试其他地址
http://127.0.0.1:5001
http://0.0.0.0:5001
```

#### Q3: Playwright浏览器安装失败
```bash
# 解决方案：
# 1. 手动安装浏览器
python -m playwright install chromium

# 2. 如果网络问题，使用国内镜像
pip install playwright -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 3. 检查系统依赖（Linux）
sudo apt-get install libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2
```

---

### 📁 文件处理问题

#### Q4: 流水匹配器找不到文件
```bash
# 问题现象：❌ 缺少必要文件: TZ.xlsx, LS.xlsx

# 解决方案：
# 1. 检查文件是否存在
ls -la *.xlsx  # macOS/Linux
dir *.xlsx     # Windows

# 2. 检查文件名大小写（区分大小写）
# 确保文件名完全匹配：TZ.xlsx, LS.xlsx

# 3. 检查文件权限
chmod 644 TZ.xlsx LS.xlsx  # macOS/Linux

# 4. 确保文件未被占用
# 关闭Excel或其他可能占用文件的程序
```

#### Q5: Excel文件格式错误
```bash
# 问题现象：文件读取失败或数据格式不正确

# 解决方案：
# 1. 检查文件格式
file TZ.xlsx  # 确认是Excel格式

# 2. 转换文件格式
# 在Excel中另存为 .xlsx 格式

# 3. 检查必需列是否存在
# TZ.xlsx: 交易类型、客户姓名、交易金额、日期、时间
# LS.xlsx: 交易时间、交易金额、交易参考号

# 4. 检查数据编码
# 确保文件使用UTF-8编码
```

#### Q6: 填充工具处理失败
```bash
# 问题现象：目标文件写入失败

# 解决方案：
# 1. 检查目标文件是否存在
ls -la TTXW.xlsx

# 2. 确保文件未被打开
# 关闭Excel或WPS等程序

# 3. 检查文件权限
chmod 666 TTXW.xlsx  # 给予读写权限

# 4. 备份并重新创建目标文件
cp TTXW.xlsx TTXW_backup.xlsx
```

---

### ⚡ 性能优化问题

#### Q7: 处理速度慢
```bash
# 账单助手处理慢：
# 1. 检查网络连接稳定性
# 2. 减少并发处理数量
# 3. 使用有线网络替代WiFi

# 流水匹配器处理慢：
# 1. 减少数据量，分批处理
# 2. 关闭不必要的程序释放内存
# 3. 使用SSD硬盘提高I/O速度

# 填充工具处理慢：
# 1. 减少源文件数量
# 2. 优化Excel文件大小
# 3. 确保足够的可用内存
```

#### Q8: 内存不足
```bash
# 问题现象：程序崩溃或系统卡顿

# 解决方案：
# 1. 增加虚拟内存
# Windows: 控制面板 → 系统 → 高级系统设置 → 性能设置 → 虚拟内存

# 2. 分批处理大文件
# 将大文件拆分为多个小文件分别处理

# 3. 关闭其他程序
# 释放系统内存供程序使用

# 4. 升级硬件
# 增加物理内存到8GB或更多
```

## 📈 更新日志

### 🎉 版本 2.0.0 (2024-12-XX) - 重大更新

#### ✨ 新增功能
- 🎉 **聚合二维码流水匹配器** - 智能匹配算法，支持多维度匹配策略
- 🎉 **多文件内容填充工具** - 批量处理Excel文件，自动化数据整合
- 🎉 **统一启动菜单** - 一键启动所有功能模块，提升用户体验
- 📖 **详细使用指南** - 完善的文档体系，降低使用门槛
- 🔧 **批处理启动器** - Windows批处理文件，简化启动流程

#### 🔧 优化改进
- 🔧 **账单助手登录逻辑优化** - 支持新旧系统，智能登录检测
- 🔧 **数据提取算法改进** - 多重备用策略，提高成功率
- 🔧 **错误处理机制增强** - 完善的异常捕获和用户友好提示
- 🔧 **用户界面体验优化** - 实时进度显示，异步任务处理
- 🔧 **性能优化** - 内存管理优化，支持大文件处理

#### 🏗️ 架构调整
- 🏗️ **模块化设计** - 各功能模块独立，便于维护和扩展
- 🏗️ **统一配置管理** - 集中化配置，支持多系统切换
- 🏗️ **标准化错误处理** - 统一的错误处理和日志记录
- 🏗️ **完善文档体系** - 详细的API文档和使用指南
- 🏗️ **代码质量提升** - 类型提示、代码规范、单元测试

#### 🐛 问题修复
- 🐛 修复账单助手在某些情况下登录失败的问题
- 🐛 修复Excel文件读取时编码错误的问题
- 🐛 修复大文件处理时内存溢出的问题
- 🐛 修复Web界面在某些浏览器下显示异常的问题

---

### 🚀 版本 1.0.0 (2024-05-XX) - 初始版本

#### ✨ 初始功能
- ✨ **账单助手系统基础功能** - Web界面的账单到期时间提取
- ✨ **Web界面和API** - Flask Web服务，RESTful API
- ✨ **自动化数据提取** - Playwright浏览器自动化
- ✨ **Excel文件处理** - 支持.xls和.xlsx格式文件
- ✨ **基础错误处理** - 简单的异常捕获和处理

#### 🛠️ 技术栈
- Python 3.8+
- Flask 2.3.3
- Playwright 1.40.0
- Pandas 2.0.3
- Openpyxl 3.1.2

## 🚀 后续开发计划

### 📅 短期计划（1-3个月）

#### 🔐 安全与认证
- [ ] **用户认证系统** - 添加登录验证，支持多用户
- [ ] **权限管理** - 不同用户角色的功能权限控制
- [ ] **数据加密** - 敏感数据加密存储和传输

#### ⚡ 性能优化
- [ ] **数据处理性能优化** - 算法优化，提升处理速度
- [ ] **内存使用优化** - 减少内存占用，支持更大文件
- [ ] **并发处理能力** - 支持多任务并行处理

#### 🔍 功能增强
- [ ] **数据验证功能** - 增强数据格式和完整性验证
- [ ] **错误日志系统** - 完善的日志记录和分析
- [ ] **配置界面** - Web界面的系统配置管理

---

### 📅 中期计划（3-6个月）

#### 🔄 处理能力扩展
- [ ] **多账号并行处理** - 支持多个系统账号同时工作
- [ ] **批量任务调度** - 定时任务和批量处理调度
- [ ] **分布式处理** - 支持多机器协同处理

#### 📊 数据可视化
- [ ] **数据可视化仪表板** - 实时数据监控和分析
- [ ] **报表生成系统** - 自动生成各类业务报表
- [ ] **数据统计分析** - 深度数据分析和洞察

#### 📁 格式支持扩展
- [ ] **更多文件格式支持** - CSV, JSON, XML等格式
- [ ] **数据库连接** - 支持MySQL, PostgreSQL等数据库
- [ ] **API集成** - 与第三方系统API集成

---

### 📅 长期计划（6-12个月）

#### 🌐 平台化发展
- [ ] **RESTful API接口** - 提供完整的API服务
- [ ] **微服务架构** - 模块化微服务部署
- [ ] **云端部署支持** - 支持Docker容器化部署

#### 📱 移动端支持
- [ ] **移动端Web应用** - 响应式设计，支持移动设备
- [ ] **移动端原生应用** - iOS/Android原生应用
- [ ] **离线处理能力** - 支持离线数据处理

#### 🤖 智能化升级
- [ ] **机器学习算法集成** - 智能匹配算法优化
- [ ] **自动化决策系统** - 基于历史数据的智能决策
- [ ] **异常检测系统** - 自动识别数据异常和风险

#### 📈 数据分析平台
- [ ] **综合数据分析平台** - 一站式数据分析解决方案
- [ ] **商业智能(BI)功能** - 深度业务分析和预测
- [ ] **数据挖掘工具** - 高级数据挖掘和模式识别

---

### 🎯 技术发展方向

#### 前端技术升级
- [ ] **现代化前端框架** - Vue.js/React.js重构前端
- [ ] **实时通信** - WebSocket实时数据推送
- [ ] **PWA支持** - 渐进式Web应用

#### 后端技术演进
- [ ] **异步处理框架** - FastAPI/AsyncIO异步处理
- [ ] **消息队列** - Redis/RabbitMQ任务队列
- [ ] **缓存系统** - Redis缓存优化

#### 运维与监控
- [ ] **监控系统** - 系统性能和业务监控
- [ ] **日志分析** - ELK Stack日志分析
- [ ] **自动化运维** - CI/CD自动化部署

## 🤝 贡献指南

### 🛠️ 开发环境设置

#### 环境准备
```bash
# 1. Fork项目到你的GitHub账号
# 2. 克隆项目到本地
git clone https://github.com/your-username/Bill-Helper.git
cd Bill-Helper

# 3. 添加上游仓库
git remote add upstream https://github.com/original-repo/Bill-Helper.git

# 4. 创建开发分支
git checkout -b feature/your-feature-name

# 5. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate     # Windows

# 6. 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 如果存在

# 7. 安装pre-commit钩子（推荐）
pip install pre-commit
pre-commit install
```

#### 开发工具推荐
```bash
# 代码编辑器
- VS Code (推荐)
- PyCharm
- Sublime Text

# 必需插件/扩展
- Python语法高亮
- 代码格式化 (black, autopep8)
- 类型检查 (mypy)
- 代码检查 (flake8, pylint)
```

---

### 📝 代码规范

#### Python代码规范
```python
# 1. 遵循PEP 8编码规范
# 2. 使用类型提示
def process_data(data: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    处理数据的函数

    Args:
        data: 输入数据列表

    Returns:
        处理后的DataFrame
    """
    pass

# 3. 添加文档字符串
class DataProcessor:
    """
    数据处理器类

    负责处理Excel文件数据的读取、转换和输出
    """

    def __init__(self, config: Dict[str, Any]) -> None:
        """初始化数据处理器"""
        pass

# 4. 使用有意义的变量名
user_data = load_user_data()  # 好
data = load_data()           # 不好
```

#### 文件组织规范
```
新功能开发目录结构：
├── bill-helper/modules/new_feature/
│   ├── __init__.py
│   ├── main.py              # 主要功能实现
│   ├── utils.py             # 工具函数
│   ├── config.py            # 配置文件
│   └── tests/               # 单元测试
│       ├── __init__.py
│       ├── test_main.py
│       └── test_utils.py
└── docs/
    └── new_feature.md       # 功能文档
```

---

### 🧪 测试规范

#### 单元测试
```python
# tests/test_example.py
import unittest
from unittest.mock import patch, MagicMock
from bill_helper.modules.processor.data_processor import DataProcessor

class TestDataProcessor(unittest.TestCase):

    def setUp(self):
        """测试前准备"""
        self.processor = DataProcessor()

    def test_process_valid_data(self):
        """测试有效数据处理"""
        # 准备测试数据
        test_data = [{"id": 1, "name": "test"}]

        # 执行测试
        result = self.processor.process(test_data)

        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 1)

    @patch('bill_helper.modules.processor.data_processor.pd.read_excel')
    def test_load_excel_file(self, mock_read_excel):
        """测试Excel文件加载（使用Mock）"""
        # 设置Mock返回值
        mock_read_excel.return_value = MagicMock()

        # 执行测试
        result = self.processor.load_excel("test.xlsx")

        # 验证Mock被调用
        mock_read_excel.assert_called_once_with("test.xlsx")

# 运行测试
if __name__ == '__main__':
    unittest.main()
```

#### 测试命令
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试文件
python -m pytest tests/test_data_processor.py

# 运行测试并生成覆盖率报告
python -m pytest tests/ --cov=bill_helper --cov-report=html

# 运行测试并显示详细输出
python -m pytest tests/ -v
```

---

### 📋 提交流程

#### 代码提交规范
```bash
# 1. 确保代码通过所有检查
python -m flake8 .                    # 代码风格检查
python -m mypy .                      # 类型检查
python -m pytest tests/               # 单元测试

# 2. 提交代码
git add .
git commit -m "feat: 添加新的数据处理功能

- 实现Excel文件批量处理
- 添加数据验证功能
- 优化内存使用效率

Closes #123"

# 3. 推送到远程分支
git push origin feature/your-feature-name
```

#### 提交信息规范
```
提交信息格式：
<type>(<scope>): <subject>

<body>

<footer>

类型(type)：
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

示例：
feat(matcher): 添加智能匹配算法

实现基于多维度的智能匹配算法，包括：
- 时间优先级匹配
- 金额优先级匹配
- 客户唯一性验证

Closes #456
```

#### Pull Request流程
```bash
# 1. 创建Pull Request
# 在GitHub上创建PR，填写详细描述

# 2. PR描述模板
## 变更说明
- [ ] 新功能
- [ ] Bug修复
- [ ] 文档更新
- [ ] 性能优化

## 测试
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 没有引入新的依赖

## 相关Issue
Closes #123

# 3. 代码审查
# 等待维护者审查代码并提供反馈

# 4. 合并代码
# 审查通过后，维护者会合并PR
```

## 📄 许可证

本项目采用 **MIT 许可证**，这意味着：

✅ **允许的使用方式：**
- 商业使用
- 修改代码
- 分发代码
- 私人使用

📋 **使用条件：**
- 保留版权声明
- 保留许可证声明

❌ **不提供保证：**
- 不承担责任
- 不提供担保

详情请参见 [LICENSE](LICENSE) 文件。

---

## 🆘 技术支持

### 📚 获取帮助

| 资源类型 | 链接 | 说明 |
|----------|------|------|
| 📖 **详细使用指南** | [使用指南.md](使用指南.md) | 完整的功能使用说明 |
| 📋 **产品需求文档** | [产品需求文档.md](产品需求文档.md) | 产品设计和需求说明 |
| 🐛 **问题报告** | [GitHub Issues](../../issues) | 提交Bug报告和功能请求 |
| 💬 **讨论交流** | [GitHub Discussions](../../discussions) | 社区讨论和经验分享 |
| 📖 **API文档** | [Wiki](../../wiki) | 详细的API使用文档 |

### 🔍 问题排查步骤

1. **查看常见问题** - 先查看本文档的"常见问题与解决方案"部分
2. **检查使用指南** - 确认操作步骤是否正确
3. **查看日志信息** - 检查程序输出的错误信息
4. **搜索已知问题** - 在GitHub Issues中搜索类似问题
5. **提交新问题** - 如果问题未解决，创建新的Issue

### 📞 联系方式

| 联系方式 | 信息 | 响应时间 |
|----------|------|----------|
| 🐛 **Bug报告** | [GitHub Issues](../../issues) | 1-3个工作日 |
| 💡 **功能建议** | [GitHub Discussions](../../discussions) | 3-7个工作日 |
| 📧 **技术支持** | [<EMAIL>](mailto:<EMAIL>) | 1-2个工作日 |
| 📱 **紧急联系** | 仅限严重生产问题 | 24小时内 |

### 🤝 社区支持

- 💬 **用户交流群** - 加入用户交流群获取实时帮助
- 📚 **知识库** - 查看社区维护的知识库和FAQ
- 🎥 **视频教程** - 观看详细的操作视频教程
- 📝 **博客文章** - 阅读最佳实践和使用技巧

---

## 🙏 致谢

感谢所有为 Bill-Helper 项目做出贡献的开发者和用户！

### 🏆 主要贡献者
- **项目发起人** - 项目架构设计和核心功能开发
- **社区贡献者** - Bug修复、功能改进和文档完善
- **测试用户** - 提供宝贵的使用反馈和建议

### 🛠️ 技术栈致谢
- **Flask** - 优秀的Python Web框架
- **Playwright** - 强大的浏览器自动化工具
- **Pandas** - 高效的数据处理库
- **Openpyxl** - Excel文件操作库

### 📖 文档和设计
- **Markdown** - 文档编写格式
- **Mermaid** - 图表绘制工具
- **GitHub** - 代码托管和协作平台

---

## 📊 项目统计

| 指标 | 数值 | 说明 |
|------|------|------|
| 🌟 **GitHub Stars** | ![GitHub stars](https://img.shields.io/github/stars/username/Bill-Helper) | 项目受欢迎程度 |
| 🍴 **Forks** | ![GitHub forks](https://img.shields.io/github/forks/username/Bill-Helper) | 项目被复制次数 |
| 🐛 **Issues** | ![GitHub issues](https://img.shields.io/github/issues/username/Bill-Helper) | 当前开放问题数 |
| 📝 **Pull Requests** | ![GitHub pull requests](https://img.shields.io/github/issues-pr/username/Bill-Helper) | 待处理的PR数量 |
| 📅 **最后提交** | ![GitHub last commit](https://img.shields.io/github/last-commit/username/Bill-Helper) | 项目活跃度 |
| 📦 **代码大小** | ![GitHub repo size](https://img.shields.io/github/repo-size/username/Bill-Helper) | 项目代码大小 |

---

<div align="center">

## 🎯 项目愿景

**让数据处理变得简单高效，让财务工作更加智能化**

---

### 📱 关注我们

[![GitHub](https://img.shields.io/badge/GitHub-Bill--Helper-blue?logo=github)](https://github.com/username/Bill-Helper)
[![Documentation](https://img.shields.io/badge/Docs-使用指南-green?logo=gitbook)](使用指南.md)
[![License](https://img.shields.io/badge/License-MIT-yellow?logo=opensourceinitiative)](LICENSE)

---

*最后更新时间：2024年12月*
*项目版本：v2.0.0*
*文档版本：v2.0.0*

**⭐ 如果这个项目对你有帮助，请给我们一个Star！**

</div>
