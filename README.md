# Bill-Helper 项目文档

## 1. 项目概述

Bill-Helper是一个综合性的数据处理和自动化工具集，包含三个核心功能模块，旨在提高财务数据处理、流水匹配和文件管理的效率。该项目整合了Web应用、智能匹配算法和批量数据处理功能，为用户提供完整的数据处理解决方案。

### 1.1 核心模块

1. **账单助手系统** - 基于Flask的Web应用，自动化提取订单账单到期时间
2. **聚合二维码流水匹配器** - 智能匹配聚合二维码交易与银行流水
3. **多文件内容填充工具** - 批量处理Excel文件数据填充

### 1.2 背景与价值

在日常运营和财务管理过程中，数据处理工作繁琐且容易出错：
- 获取订单账单到期时间需要人工逐个查询
- 聚合二维码交易与银行流水匹配复杂
- 多个Excel文件的数据整合耗时费力

Bill-Helper通过自动化整个流程，显著提高了工作效率和数据准确性。

### 1.3 目标用户

- 财务人员
- 运营人员
- 数据分析师
- 管理层人员

## 2. 功能模块详解

### 2.1 账单助手系统 📋

#### 核心功能
- **文件上传与处理** - 支持上传包含订单ID的Excel文件
- **自动化登录** - 智能登录运营管理平台
- **账单数据提取** - 自动提取每期账单到期日期
- **数据处理与格式化** - 生成标准化Excel报表
- **任务管理** - 异步处理，实时进度显示

#### 技术特点
- 基于Flask的Web界面
- 使用Playwright进行浏览器自动化
- 支持多重备用数据提取策略
- 异步任务处理，不阻塞界面

#### 使用方式
```bash
# 启动Web服务
python bill-helper/app.py
# 访问 http://localhost:5001
```

### 2.2 聚合二维码流水匹配器 🔍

#### 核心功能
- **智能交易匹配** - 基于时间、金额、客户等多维度匹配
- **优先级策略** - 四层优先级算法确保最优匹配
- **冲突解决** - 自动解决匹配冲突，确保唯一性
- **综合报告** - 生成详细的匹配分析报告

#### 匹配策略
- **时间优先级** (40%) - 时间差越小越优先
- **金额优先级** (25%) - 大金额交易优先
- **客户唯一性** (20%) - 单一客户单日同金额优先
- **订单完整性** (15%) - 有备注信息优先

#### 输出文件
- `TZ_完整匹配结果.xlsx` - 完整数据+匹配结果
- `聚合二维码匹配结果.xlsx` - 仅聚合二维码结果
- `综合匹配报告.xlsx` - 详细分析报告
- `聚合二维码交易记录_提取.xlsx` - 原始提取数据

#### 使用方式
```bash
# 命令行交互界面
python qr_flow_matcher.py
# 需要准备：TZ.xlsx + LS.xlsx
```

### 2.3 多文件内容填充工具 📊

#### 核心功能
- **批量文件处理** - 同时处理多个Excel文件
- **智能数据映射** - 自动处理10个还款期数据
- **双表填充** - 同时填充"订单管理"和"资金流水账"
- **数据排序** - 按日期自动排序

#### 数据处理逻辑
- 基础字段直接映射(A-M列)
- 还款期数据智能处理(N-W列)
- 自动生成放款和供应商利润记录
- 支持日期格式自动转换

#### 交互菜单
1. 选择源文件
2. 预览数据
3. 执行填充
4. 查看已选择文件
5. 设置目标文件
6. 退出

#### 使用方式
```bash
# 交互式菜单界面
python multi_file_filler_correct.py
# 目标文件：TTXW.xlsx
```

## 3. 系统架构

### 3.1 整体架构

```
Bill-Helper 项目架构
├── 账单助手系统 (Web应用)
│   ├── Flask Web服务
│   ├── Playwright爬虫
│   └── 异步任务处理
├── 聚合二维码流水匹配器 (命令行应用)
│   ├── 数据提取器
│   ├── 智能匹配算法
│   └── 结果合并器
└── 多文件内容填充工具 (交互式应用)
    ├── 文件扫描器
    ├── 数据处理器
    └── Excel写入器
```

### 3.2 文件结构

```
Bill-Helper/
├── bill-helper/                    # 账单助手系统
│   ├── app.py                     # Web应用主入口
│   ├── config.py                  # 配置文件
│   ├── modules/                   # 功能模块
│   │   ├── auth/                  # 认证模块
│   │   ├── crawler/               # 爬虫模块
│   │   ├── processor/             # 数据处理模块
│   │   └── exporter/              # 导出模块
│   ├── templates/                 # 模板文件
│   └── requirements.txt           # 依赖包
├── qr_flow_matcher.py             # 流水匹配器主程序
├── enhanced_qr_matcher.py         # 增强匹配器
├── qr_data_merger.py              # 数据合并器
├── qr_transaction_extractor.py    # 交易提取器
├── multi_file_filler_correct.py   # 多文件填充工具
├── 使用指南.md                    # 详细使用指南
├── 启动菜单.bat                   # 主启动菜单
├── 启动账单助手.bat               # 账单助手启动器
├── 启动流水匹配器.bat             # 流水匹配器启动器
├── 启动填充工具.bat               # 填充工具启动器
└── README.md                      # 项目文档
```

## 4. 技术栈

### 4.1 后端技术

- **语言**: Python 3.8+
- **Web框架**: Flask
- **数据处理**: Pandas, NumPy
- **文件操作**: Openpyxl, Xlrd
- **浏览器自动化**: Playwright
- **日期处理**: datetime, timedelta
- **类型提示**: typing

### 4.2 前端技术

- **界面框架**: HTML5, CSS3, JavaScript
- **页面交互**: jQuery, AJAX
- **进度显示**: 实时进度条
- **命令行界面**: 彩色终端输出

### 4.3 数据格式

- **输入格式**: Excel (.xlsx, .xls)
- **输出格式**: Excel (.xlsx)
- **数据编码**: UTF-8
- **日期格式**: 多种格式自动识别

## 5. 快速开始

### 5.1 环境要求

- Python 3.8+
- 支持的操作系统: Windows, macOS, Linux
- Chrome或Edge浏览器（账单助手需要）
- 至少4GB内存

### 5.2 安装步骤

```bash
# 1. 克隆项目
git clone <repository-url>
cd Bill-Helper

# 2. 创建虚拟环境
python -m venv venv

# 3. 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt

# 5. 安装浏览器（账单助手需要）
python -m playwright install chromium
```

### 5.3 快速启动

#### 方式一：使用启动菜单（推荐）
```bash
# 双击启动菜单.bat文件
# 或在命令行中运行：
启动菜单.bat
```

#### 方式二：直接启动
```bash
# 账单助手系统
python bill-helper/app.py

# 聚合二维码流水匹配器
python qr_flow_matcher.py

# 多文件内容填充工具
python multi_file_filler_correct.py
```

## 6. 使用指南

### 6.1 账单助手系统使用

1. **启动服务**
   ```bash
   python bill-helper/app.py
   ```

2. **访问界面**
   - 打开浏览器访问：`http://localhost:5001`

3. **上传文件**
   - 选择包含"订单ID"列的Excel文件
   - 系统自动验证文件格式

4. **配置参数**
   - 输入系统用户名和密码
   - 选择系统类型

5. **执行处理**
   - 点击"开始处理"
   - 查看实时进度
   - 下载结果文件

### 6.2 聚合二维码流水匹配器使用

1. **准备文件**
   - 确保当前目录有 `TZ.xlsx` 和 `LS.xlsx`

2. **运行程序**
   ```bash
   python qr_flow_matcher.py
   ```

3. **查看数据概览**
   - 系统显示数据统计信息

4. **确认执行**
   - 输入 `y` 或 `yes` 确认

5. **查看结果**
   - 匹配成功率和质量分布
   - 生成的结果文件

### 6.3 多文件内容填充工具使用

1. **准备文件**
   - 确保有目标文件 `TTXW.xlsx`
   - 准备源Excel文件

2. **运行程序**
   ```bash
   python multi_file_filler_correct.py
   ```

3. **选择源文件**
   - 选择菜单项 1
   - 输入文件序号或 `all`

4. **预览数据**（可选）
   - 选择菜单项 2
   - 查看数据格式

5. **执行填充**
   - 选择菜单项 3
   - 确认操作

## 7. 文件格式要求

### 7.1 账单助手系统

**输入文件格式**：
| 订单ID | 客户名称 | 产品信息 | ... |
|--------|----------|----------|-----|
| OI1805817558897655808 | 张三 | 产品A | ... |

**输出文件格式**：
| 订单ID | 客户名称 | ... | 账单到期日期 | 第一期 | 第二期 | ... |
|--------|----------|-----|------------|-------|-------|-----|
| OI1805817558897655808 | 张三 | ... | 2025-06-01 | 2025-06-01 | 2025-07-01 | ... |

### 7.2 聚合二维码流水匹配器

**TZ.xlsx要求**：
- 必须包含"交易类型"列
- 必须有"聚合二维码"类型的记录
- 包含"客户姓名"、"交易金额"、"日期"、"时间"等列

**LS.xlsx要求**：
- 必须包含"交易时间"列
- 必须包含"交易金额"列
- 必须包含"交易参考号"列

### 7.3 多文件内容填充工具

**源文件要求**：
- 标准的订单数据格式
- 包含日期、订单编号、客户信息等基础字段
- 包含还款期数据（最多支持6期）

**目标文件要求**：
- TTXW.xlsx格式
- 包含"订单管理"和"资金流水账"工作表

## 8. 性能与安全

### 8.1 性能特性

- **账单助手**: 支持处理1000+订单，平均5秒/订单
- **流水匹配器**: 支持万级别交易记录匹配
- **填充工具**: 支持批量处理多个大型Excel文件
- **内存优化**: 分批处理，避免内存溢出

### 8.2 安全措施

- 登录信息安全处理
- 文件类型和大小验证
- 随机文件名防止覆盖
- 敏感信息日志脱敏
- 本地处理，数据不上传外部服务器

## 9. 常见问题与解决方案

### 9.1 环境问题

**Q: 提示"模块未找到"**
```bash
# 确保安装了所有依赖
pip install -r requirements.txt
```

**Q: 账单助手无法访问网页**
- 检查5001端口是否被占用
- 确保防火墙允许Python访问网络
- 尝试访问 `http://127.0.0.1:5001`

### 9.2 数据处理问题

**Q: 流水匹配器找不到文件**
- 确保TZ.xlsx和LS.xlsx在当前目录
- 检查文件名大小写
- 确保文件未被其他程序占用

**Q: 填充工具处理失败**
- 检查目标文件TTXW.xlsx是否存在
- 确保Excel文件未被打开
- 检查源文件格式是否正确

### 9.3 性能问题

**Q: 处理速度慢**
- 检查网络连接（账单助手）
- 减少批处理数量
- 确保足够的内存空间

**Q: 内存不足**
- 分批处理大文件
- 关闭不必要的程序
- 增加虚拟内存

## 10. 更新日志

### 版本 2.0.0 (2024-12-XX)

#### 新增功能
- 🎉 新增聚合二维码流水匹配器
- 🎉 新增多文件内容填充工具
- 🎉 新增统一启动菜单
- 📖 新增详细使用指南

#### 优化改进
- 🔧 优化账单助手登录逻辑
- 🔧 改进数据提取算法
- 🔧 增强错误处理机制
- 🔧 优化用户界面体验

#### 架构调整
- 🏗️ 模块化设计，各功能独立
- 🏗️ 统一的配置管理
- 🏗️ 标准化的错误处理
- 🏗️ 完善的文档体系

### 版本 1.0.0 (2024-05-XX)

#### 初始版本
- ✨ 账单助手系统基础功能
- ✨ Web界面和API
- ✨ 自动化数据提取
- ✨ Excel文件处理

## 11. 后续开发计划

### 短期计划（1-3个月）
- [ ] 添加用户认证系统
- [ ] 优化数据处理性能
- [ ] 增加数据验证功能
- [ ] 完善错误日志系统

### 中期计划（3-6个月）
- [ ] 实现多账号并行处理
- [ ] 添加数据可视化功能
- [ ] 支持更多文件格式
- [ ] 增加定时任务功能

### 长期计划（6-12个月）
- [ ] 提供API接口
- [ ] 开发移动端应用
- [ ] 集成机器学习算法
- [ ] 构建数据分析平台

## 12. 贡献指南

### 12.1 开发环境设置

```bash
# 1. Fork项目
# 2. 克隆到本地
git clone <your-fork-url>
cd Bill-Helper

# 3. 创建开发分支
git checkout -b feature/your-feature

# 4. 安装开发依赖
pip install -r requirements-dev.txt

# 5. 运行测试
python -m pytest tests/
```

### 12.2 代码规范

- 遵循PEP 8编码规范
- 添加适当的类型提示
- 编写单元测试
- 更新相关文档

### 12.3 提交流程

1. 确保代码通过所有测试
2. 更新相关文档
3. 提交Pull Request
4. 等待代码审查

## 13. 许可证

本项目采用MIT许可证，详情请参见[LICENSE](LICENSE)文件。

## 14. 技术支持

### 14.1 获取帮助

- 📖 查看[使用指南.md](使用指南.md)
- 🐛 提交Issue报告问题
- 💬 参与讨论交流

### 14.2 联系方式

- 项目维护者：[维护者信息]
- 技术支持：[支持邮箱]
- 官方网站：[项目网站]

---

*最后更新时间：2024年12月*
*项目版本：v2.0.0*
