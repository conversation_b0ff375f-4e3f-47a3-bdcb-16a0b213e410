#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据导出模块
负责将处理后的数据导出为Excel文件
"""

import pandas as pd
import os
from datetime import datetime

def export_to_excel(data, template_path=None, output_path=None):
    """
    将数据导出为Excel文件
    
    参数:
    - data: 处理后的数据（DataFrame）
    - template_path: 模板文件路径（可选）
    - output_path: 输出文件路径（可选）
    
    返回:
    - success: 是否成功
    - output_path: 输出文件路径
    - error: 错误信息（如有）
    """
    try:
        # 如果没有提供输出路径，创建一个默认的
        if not output_path:
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"处理后的订单数据_{current_time}.xlsx"
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 如果提供了模板，尝试使用模板格式
        if template_path and os.path.exists(template_path):
            try:
                # 确定模板的引擎
                template_extension = os.path.splitext(template_path)[1].lower()
                engine = 'openpyxl' if template_extension == '.xlsx' else 'xlrd'
                
                # 读取模板结构（不修改原始模板）
                template_df = pd.read_excel(template_path, engine=engine)
                
                # 创建与模板结构匹配的数据
                template_columns = template_df.columns.tolist()
                
                # 创建一个新的DataFrame，列与模板匹配
                export_df = pd.DataFrame(columns=template_columns)
                
                # 复制数据到新的DataFrame，根据列名匹配
                for col in template_columns:
                    if col in data.columns:
                        export_df[col] = data[col]
                
                # 保存结果
                export_df.to_excel(output_path, index=False)
                print(f"已成功导出数据到模板格式: {output_path}")
                return True, output_path, None
            except Exception as e:
                print(f"使用模板导出失败: {str(e)}，将使用标准格式导出")
        
        # 标准导出（不使用模板或模板导出失败）
        data.to_excel(output_path, index=False)
        print(f"已成功导出数据: {output_path}")
        return True, output_path, None
    except Exception as e:
        error_msg = f"导出Excel时出错: {str(e)}"
        print(error_msg)
        return False, None, error_msg

def export_to_csv(data, output_path=None):
    """
    将数据导出为CSV文件
    
    参数:
    - data: 处理后的数据（DataFrame）
    - output_path: 输出文件路径（可选）
    
    返回:
    - success: 是否成功
    - output_path: 输出文件路径
    - error: 错误信息（如有）
    """
    try:
        # 如果没有提供输出路径，创建一个默认的
        if not output_path:
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"处理后的订单数据_{current_time}.csv"
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 导出为CSV
        data.to_csv(output_path, index=False, encoding='utf-8-sig')  # 使用utf-8-sig编码以支持中文在Excel中正确显示
        print(f"已成功导出数据为CSV: {output_path}")
        return True, output_path, None
    except Exception as e:
        error_msg = f"导出CSV时出错: {str(e)}"
        print(error_msg)
        return False, None, error_msg
