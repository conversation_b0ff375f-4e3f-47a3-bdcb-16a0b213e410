@echo off
title 多文件内容填充工具
echo.
echo ===============================================
echo        多文件内容填充工具启动中...
echo ===============================================
echo.

cd /d "%~dp0"

echo 正在检查目标文件...
if not exist "TTXW.xlsx" (
    echo 警告: 找不到 TTXW.xlsx 文件
    echo 请确保 TTXW.xlsx 文件在当前目录中
    echo.
    set /p choice="是否继续启动程序？(y/n): "
    if /i not "!choice!"=="y" (
        echo 程序已取消
        pause
        exit /b 1
    )
)

echo 正在启动填充工具...
echo.

python multi_file_filler_correct.py

echo.
echo 填充工具已退出
pause 