#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from datetime import datetime
from typing import Dict, List


class QRDataMerger:
    """聚合二维码数据合并器"""
    
    def __init__(self, tz_file: str):
        """
        初始化合并器
        
        Args:
            tz_file: 原始TZ.xlsx文件路径
        """
        self.tz_file = tz_file
        self.original_df = None
        self.enhanced_df = None
        
    def load_original_data(self):
        """加载原始TZ.xlsx数据"""
        try:
            print("📂 正在加载原始TZ.xlsx数据...")
            self.original_df = pd.read_excel(self.tz_file, sheet_name='Sheet1')
            print(f"   原始数据加载成功，共 {len(self.original_df)} 条记录")
            return True
        except Exception as e:
            print(f"❌ 加载原始数据失败: {e}")
            return False
    
    def merge_match_results(self, matches_df: pd.DataFrame) -> pd.DataFrame:
        """
        将匹配结果合并回原始表格
        
        Args:
            matches_df: 匹配结果DataFrame
            
        Returns:
            合并后的增强DataFrame
        """
        if self.original_df is None:
            if not self.load_original_data():
                return pd.DataFrame()
        
        print("🔧 正在合并匹配结果...")
        
        # 创建增强版DataFrame
        self.enhanced_df = self.original_df.copy()
        
        # 新增追踪列
        self.enhanced_df['匹配状态'] = self._init_match_status()
        self.enhanced_df['原始交易订单号'] = self.enhanced_df['交易订单号']
        self.enhanced_df['匹配置信度'] = ''
        self.enhanced_df['匹配时间'] = ''
        self.enhanced_df['匹配流水号'] = ''
        
        # 应用匹配结果
        if not matches_df.empty:
            match_count = self._apply_matches(matches_df)
            print(f"   成功应用 {match_count} 个匹配结果")
        
        # 标记未匹配的聚合二维码
        self._mark_unmatched_qr()
        
        print("✅ 数据合并完成")
        return self.enhanced_df
    
    def _init_match_status(self) -> List[str]:
        """初始化匹配状态"""
        status = []
        for _, row in self.original_df.iterrows():
            if row['交易类型'] == '聚合二维码':
                status.append('待匹配')
            else:
                status.append('无需匹配')
        return status
    
    def _apply_matches(self, matches_df: pd.DataFrame) -> int:
        """应用匹配结果"""
        match_count = 0
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for _, match in matches_df.iterrows():
            original_idx = match['original_index']
            ls_serial = match['ls_serial']
            priority_score = match['priority_score']
            
            # 更新流水号和状态信息
            self.enhanced_df.at[original_idx, '交易订单号'] = ls_serial
            self.enhanced_df.at[original_idx, '匹配流水号'] = ls_serial
            self.enhanced_df.at[original_idx, '匹配状态'] = (
                f"已匹配(优先级:{priority_score:.2f})"
            )
            self.enhanced_df.at[original_idx, '匹配置信度'] = priority_score
            self.enhanced_df.at[original_idx, '匹配时间'] = current_time
            
            match_count += 1
        
        return match_count
    
    def _mark_unmatched_qr(self):
        """标记未匹配的聚合二维码"""
        unmatched_mask = (
            (self.enhanced_df['交易类型'] == '聚合二维码') & 
            (self.enhanced_df['匹配状态'] == '待匹配')
        )
        self.enhanced_df.loc[unmatched_mask, '匹配状态'] = '未匹配'
    
    def save_results(self, output_files: Dict[str, str] = None):
        """
        保存合并结果到多个文件
        
        Args:
            output_files: 输出文件名字典
        """
        if output_files is None:
            output_files = {
                'complete': 'TZ_完整匹配结果.xlsx',
                'qr_only': '聚合二维码匹配结果.xlsx',
                'report': '综合匹配报告.xlsx'
            }
        
        if self.enhanced_df is None:
            print("❌ 没有可保存的合并结果")
            return
        
        try:
            # 1. 保存完整合并结果
            self._save_complete_results(output_files['complete'])
            
            # 2. 保存仅聚合二维码结果
            self._save_qr_only_results(output_files['qr_only'])
            
            # 3. 生成综合报告
            self._generate_comprehensive_report(output_files['report'])
            
            print("✅ 所有结果文件保存完成")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def _save_complete_results(self, filename: str):
        """保存完整合并结果"""
        print(f"💾 正在保存完整结果到 {filename}...")
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            self.enhanced_df.to_excel(writer, sheet_name='完整数据', index=False)
            
            # 自动调整列宽
            self._adjust_column_width(writer, '完整数据')
    
    def _save_qr_only_results(self, filename: str):
        """保存仅聚合二维码结果"""
        print(f"💾 正在保存聚合二维码结果到 {filename}...")
        
        qr_data = self.enhanced_df[
            self.enhanced_df['交易类型'] == '聚合二维码'
        ].copy()
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            qr_data.to_excel(writer, sheet_name='聚合二维码', index=False)
            
            # 自动调整列宽
            self._adjust_column_width(writer, '聚合二维码')
    
    def _generate_comprehensive_report(self, filename: str):
        """生成综合匹配报告"""
        print(f"📊 正在生成综合报告到 {filename}...")
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # Sheet1: 匹配统计摘要
            stats_df = self._calculate_match_statistics()
            stats_df.to_excel(writer, sheet_name='匹配统计', index=False)
            
            # Sheet2: 聚合二维码详情
            qr_data = self.enhanced_df[
                self.enhanced_df['交易类型'] == '聚合二维码'
            ]
            qr_data.to_excel(writer, sheet_name='聚合二维码详情', index=False)
            
            # Sheet3: 已匹配记录
            matched_data = self.enhanced_df[
                self.enhanced_df['匹配状态'].str.contains('已匹配', na=False)
            ]
            if not matched_data.empty:
                matched_data.to_excel(writer, sheet_name='已匹配记录', index=False)
            
            # Sheet4: 未匹配记录
            unmatched_data = self.enhanced_df[
                self.enhanced_df['匹配状态'] == '未匹配'
            ]
            if not unmatched_data.empty:
                unmatched_data.to_excel(writer, sheet_name='未匹配记录', index=False)
            
            # Sheet5: 完整数据（用于对比）
            self.enhanced_df.to_excel(writer, sheet_name='完整数据', index=False)
            
            # 调整所有sheet的列宽
            for sheet_name in writer.sheets:
                self._adjust_column_width(writer, sheet_name)
    
    def _calculate_match_statistics(self) -> pd.DataFrame:
        """计算匹配统计信息"""
        total_records = len(self.enhanced_df)
        qr_records = len(
            self.enhanced_df[self.enhanced_df['交易类型'] == '聚合二维码']
        )
        
        matched_qr = len(
            self.enhanced_df[
                self.enhanced_df['匹配状态'].str.contains('已匹配', na=False)
            ]
        )
        
        unmatched_qr = len(
            self.enhanced_df[self.enhanced_df['匹配状态'] == '未匹配']
        )
        
        other_transactions = total_records - qr_records
        
        # 按优先级统计匹配结果
        if matched_qr > 0:
            high_priority = len(
                self.enhanced_df[
                    (self.enhanced_df['匹配置信度'] != '') & 
                    (pd.to_numeric(
                        self.enhanced_df['匹配置信度'], 
                        errors='coerce'
                    ) >= 0.8)
                ]
            )
            medium_priority = len(
                self.enhanced_df[
                    (self.enhanced_df['匹配置信度'] != '') & 
                    (pd.to_numeric(
                        self.enhanced_df['匹配置信度'], 
                        errors='coerce'
                    ).between(0.6, 0.8, inclusive='left'))
                ]
            )
            low_priority = matched_qr - high_priority - medium_priority
        else:
            high_priority = medium_priority = low_priority = 0
        
        stats_data = [
            ['总记录数', total_records],
            ['聚合二维码记录数', qr_records],
            ['其他交易类型记录数', other_transactions],
            ['', ''],
            ['匹配结果统计', ''],
            ['成功匹配数', matched_qr],
            ['未匹配数', unmatched_qr],
            ['匹配成功率', f"{matched_qr/qr_records*100:.1f}%" if qr_records > 0 else "0%"],
            ['', ''],
            ['匹配质量分析', ''],
            ['高优先级匹配(≥0.8)', high_priority],
            ['中优先级匹配(0.6-0.8)', medium_priority],
            ['低优先级匹配(<0.6)', low_priority]
        ]
        
        return pd.DataFrame(stats_data, columns=['统计项', '数值'])
    
    def _adjust_column_width(self, writer, sheet_name: str):
        """自动调整列宽"""
        worksheet = writer.sheets[sheet_name]
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    cell_length = len(str(cell.value))
                    if cell_length > max_length:
                        max_length = cell_length
                except Exception:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def get_merge_summary(self) -> Dict:
        """获取合并结果摘要"""
        if self.enhanced_df is None:
            return {}
        
        total_records = len(self.enhanced_df)
        qr_records = len(
            self.enhanced_df[self.enhanced_df['交易类型'] == '聚合二维码']
        )
        matched_qr = len(
            self.enhanced_df[
                self.enhanced_df['匹配状态'].str.contains('已匹配', na=False)
            ]
        )
        unmatched_qr = len(
            self.enhanced_df[self.enhanced_df['匹配状态'] == '未匹配']
        )
        
        return {
            'total_records': total_records,
            'qr_records': qr_records,
            'matched_qr': matched_qr,
            'unmatched_qr': unmatched_qr,
            'match_rate': f"{matched_qr/qr_records*100:.1f}%" if qr_records > 0 else "0%",
            'other_records': total_records - qr_records
        }


def main():
    """测试函数"""
    print("🚀 聚合二维码数据合并器测试")
    print("=" * 50)
    
    merger = QRDataMerger("TZ.xlsx")
    
    if not merger.load_original_data():
        return
    
    # 创建模拟匹配结果进行测试
    test_matches = pd.DataFrame([
        {
            'original_index': 50,  # 假设索引
            'ls_serial': 'TEST_SERIAL_001',
            'priority_score': 0.85,
            'customer_name': '测试客户',
            'amount': 1000.0
        }
    ])
    
    # 执行合并
    enhanced_data = merger.merge_match_results(test_matches)
    
    if not enhanced_data.empty:
        print("\n📊 合并结果摘要:")
        summary = merger.get_merge_summary()
        for key, value in summary.items():
            print(f"   {key}: {value}")
        
        # 保存结果
        merger.save_results()
    
    print("\n🎉 合并器测试完成！")


if __name__ == "__main__":
    main() 