我有个需求，现在我们整理了某个时间范围的客户支付流水，也匹配了该笔支付的交易流水号，现在需要利用数据库（.db）,该表中有订单编号和订单的账单日期，账单的状态，也有客户姓名，我们根据这三条信息，来进行匹配整理的支付流水，具体如何匹配：
还是需要进行判断，首先还是从客户姓名进行匹配，先缩小范围，因为客户会存在多笔订单，因此，需要根据账单状态和账单带起日期进一步缩小范围，定位到置信度最高的订单；
订单有多个状态，在数据库中，订单的账单日期有多个账单日期，能进入筛选的应该是“逾期未支付”，“待支付”，“未到期”的状态，才能进入筛选范围，接下来就是找到最能与支付时间和账单到期日期最接近的订单，匹配成功后，在excel表格中新增几列填充以下内容：订单编号，订单的账单是第几期。
还有一些情况需要补充，有些匹配会有多条置信度相同的多个订单，这个时候，我们应该将金额均分，如果除不尽，那么向下取整将多余的金额填充到最后一条订单中。
因为是关于财务客户的支付流水匹配，因此我们应该在执行一条匹配后，将匹配结果打印到终端，然后询问客户是否确认，如果确认，那么将该条支付流水的匹配结果写入到excel表格中，然后再进行下一个订单的匹配。




以下是数据库的表结构：
# 数据库表结构与关系汇总

---

## 1. 主要业务表结构

### 1.1 订单表（orders）
| 字段名             | 类型         | 说明                   |
|--------------------|--------------|------------------------|
| id                 | Integer      | 主键，自增             |
| order_date         | Date         | 订单日期               |
| order_number       | String(50)   | 订单编号，唯一索引     |
| customer_name      | String(100)  | 客户姓名，索引         |
| model              | String(100)  | 型号                   |
| customer_attribute | String(50)   | 客户属性               |
| usage              | String(100)  | 用途                   |
| payment_cycle      | String(50)   | 还款周期               |
| product_type       | String(50)   | 产品类型，索引         |
| periods            | Integer      | 期数                   |
| business_type      | String(50)   | 业务类型               |
| total_receivable   | Float        | 总应收                 |
| current_receivable | Float        | 当前应收               |
| remarks            | Text         | 备注                   |
| cost               | Float        | 成本，自动计算         |
| shop_affiliation   | String(100)  | 店铺归属               |

### 1.2 还款计划表（payment_schedules）
| 字段名         | 类型       | 说明                             |
|----------------|------------|----------------------------------|
| id             | Integer    | 主键，自增                       |
| order_id       | Integer    | 订单ID，外键（orders.id）         |
| period_number  | Integer    | 期数                             |
| due_date       | Date       | 应还日期                         |
| amount         | Float      | 应还金额                         |
| paid_amount    | Float      | 已还金额，自动统计                |
| status         | String(20) | 还款状态（未到期、按时还款等）    |

### 1.3 交易表（transactions）
| 字段名                    | 类型         | 说明                         |
|---------------------------|--------------|------------------------------|
| id                        | Integer      | 主键，自增                   |
| transaction_date          | Date         | 交易日期                     |
| order_id                  | Integer      | 订单ID，外键（orders.id）     |
| customer_name             | String(100)  | 客户姓名                     |
| model                     | String(100)  | 型号                         |
| customer_attribute        | String(50)   | 客户属性                     |
| usage                     | String(100)  | 用途                         |
| payment_cycle             | String(50)   | 还款周期                     |
| product_type              | String(50)   | 产品类型                     |
| amount                    | Float        | 交易金额                     |
| period_number             | String(50)   | 归属期数                     |
| transaction_type          | String(50)   | 交易类型                     |
| direction                 | String(50)   | 资金方向                     |
| transaction_order_number  | String(50)   | 交易流水号                   |
| available_balance         | Float        | 可用余额                     |
| pending_withdrawal        | Float        | 待提现金额                   |
| remarks                   | Text         | 备注                         |

### 1.4 客户信息表（customer_info）
| 字段名             | 类型         | 说明                         |
|--------------------|--------------|------------------------------|
| id                 | Integer      | 主键，自增                   |
| order_id           | Integer      | 订单ID，唯一外键（orders.id） |
| order_number       | String(50)   | 订单编号，唯一索引           |
| customer_name      | String(100)  | 客户姓名                     |
| phone              | String(20)   | 手机号码                     |
| rental_period      | String(50)   | 租期                         |
| customer_service   | String(50)   | 客服归属                     |
| business_affiliation | String(50) | 业务归属                     |
| remarks            | Text         | 备注                         |

---

## 2. 表间关系与ER图

```mermaid
erDiagram
    orders ||--o{ payment_schedules : "订单ID"
    orders ||--o{ transactions      : "订单ID"
    orders ||--|| customer_info     : "订单ID"
```

- 一个订单（orders）可以对应多个还款计划（payment_schedules）
- 一个订单（orders）可以有多条资金流水（transactions）
- 一个订单（orders）对应一条客户信息（customer_info）

---

## 3. 字段详细说明与业务含义

### 3.1 订单表 orders
- `cost`：自动计算，包含放款和供应商利润等相关支出。
- `shop_affiliation`：Excel中“店铺名称”字段。

### 3.2 还款计划表 payment_schedules
- `paid_amount`：自动统计每期已还款总额。
- `status`：常见值有“未到期”“按时还款”“逾期未还”“逾期还款”“提前还款”“协商结清”。

### 3.3 交易表 transactions
- `product_type`：与订单表一致。
- `period_number`：部分为数字，部分为空或特殊标记。
- `transaction_type`：如首付款、租金、尾款、放款、供应商利润等。
- `direction`：区分收入/支出。
- `transaction_order_number`：交易流水号。
- `available_balance`/`pending_withdrawal`：部分业务场景使用。

### 3.4 客户信息表 customer_info
- `remarks`：通常为Excel“@芳会资料补充”中的备注。
